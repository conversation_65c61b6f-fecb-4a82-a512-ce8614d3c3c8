import subprocess
import ipaddress
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

def ping_single_host(ip, timeout=1, count=1):
    """Ping a single host and return result."""
    try:
        result = subprocess.run(['ping', '-c', str(count), '-W', str(timeout), str(ip)], 
                              capture_output=True, text=True)
        return str(ip), result.returncode == 0
    except Exception as e:
        return str(ip), False

def scan_network(network="*************/24", max_workers=50, timeout=1):
    """Scan network for active hosts with threading and progress indicator."""
    try:
        net = ipaddress.IPv4Network(network)
    except ValueError as e:
        print(f"Error: Invalid network format - {e}")
        return []
    
    active_hosts = []
    total_hosts = len(list(net.hosts()))
    completed = 0
    
    print(f"Scanning {total_hosts} hosts in {network}...")
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all ping tasks
        future_to_ip = {executor.submit(ping_single_host, ip, timeout): ip 
                       for ip in net.hosts()}
        
        # Process completed tasks
        for future in as_completed(future_to_ip):
            ip, is_active = future.result()
            completed += 1
            
            if is_active:
                active_hosts.append(ip)
                print(f"✓ {ip} is active")
            
            # Progress indicator
            progress = (completed / total_hosts) * 100
            print(f"Progress: {progress:.1f}% ({completed}/{total_hosts})", end='\r')
    
    print(f"\nScan complete! Found {len(active_hosts)} active hosts.")
    return active_hosts
